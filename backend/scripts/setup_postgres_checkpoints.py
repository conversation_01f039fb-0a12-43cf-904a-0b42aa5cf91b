#!/usr/bin/env python3
"""
Setup script for PostgreSQL checkpointing database
Creates the necessary database and tables for LangGraph checkpoints
"""
import os
import sys
import asyncio
import psycopg
from psycopg import sql
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

async def create_checkpoint_database():
    """Create PostgreSQL database for checkpoints if it doesn't exist"""
    
    # Parse the connection URL to get database info
    postgres_url = os.getenv("POSTGRES_CHECKPOINT_URL")
    if not postgres_url:
        print("❌ POSTGRES_CHECKPOINT_URL not set in environment variables")
        print("Please set it in your .env file:")
        print("POSTGRES_CHECKPOINT_URL=postgresql://username:password@localhost:5432/langgraph_checkpoints")
        return False
    
    try:
        # Parse URL to extract database name and connection info
        from urllib.parse import urlparse
        parsed = urlparse(postgres_url)
        
        db_name = parsed.path.lstrip('/')
        base_url = f"postgresql://{parsed.username}:{parsed.password}@{parsed.hostname}:{parsed.port}/postgres"
        
        print(f"🔧 Setting up PostgreSQL checkpoint database: {db_name}")
        
        # Connect to postgres database to create our target database
        async with await psycopg.AsyncConnection.connect(base_url) as conn:
            conn.autocommit = True
            async with conn.cursor() as cur:
                # Check if database exists
                await cur.execute(
                    "SELECT 1 FROM pg_database WHERE datname = %s",
                    (db_name,)
                )
                exists = await cur.fetchone()
                
                if not exists:
                    print(f"📦 Creating database: {db_name}")
                    await cur.execute(
                        sql.SQL("CREATE DATABASE {}").format(sql.Identifier(db_name))
                    )
                    print(f"✅ Database {db_name} created successfully")
                else:
                    print(f"✅ Database {db_name} already exists")
        
        # Now setup the checkpoint tables
        print("🔧 Setting up LangGraph checkpoint tables...")
        
        # Import and setup the checkpointer
        from langgraph.checkpoint.postgres.aio import AsyncPostgresSaver
        
        async with AsyncPostgresSaver.from_conn_string(postgres_url) as checkpointer:
            await checkpointer.setup()
            print("✅ LangGraph checkpoint tables created successfully")
        
        print("🎉 PostgreSQL checkpoint setup completed!")
        print(f"📝 Connection URL: {postgres_url}")
        print("🚀 You can now use PostgreSQL-based checkpointing in production")
        
        return True
        
    except Exception as e:
        print(f"❌ Error setting up PostgreSQL checkpoints: {e}")
        print("\n🔍 Troubleshooting:")
        print("1. Make sure PostgreSQL is running")
        print("2. Check your connection URL format")
        print("3. Verify username/password are correct")
        print("4. Ensure the user has database creation privileges")
        return False

def print_usage():
    """Print usage instructions"""
    print("🗄️  PostgreSQL Checkpoint Setup")
    print("=" * 40)
    print()
    print("This script sets up PostgreSQL database for LangGraph checkpointing.")
    print()
    print("Prerequisites:")
    print("1. PostgreSQL server running")
    print("2. POSTGRES_CHECKPOINT_URL set in .env file")
    print()
    print("Example .env configuration:")
    print("POSTGRES_CHECKPOINT_URL=postgresql://username:password@localhost:5432/langgraph_checkpoints")
    print()
    print("Usage:")
    print("python scripts/setup_postgres_checkpoints.py")
    print()

async def main():
    """Main setup function"""
    print_usage()
    
    # Check if URL is configured
    postgres_url = os.getenv("POSTGRES_CHECKPOINT_URL")
    if not postgres_url:
        print("⚠️  Please configure POSTGRES_CHECKPOINT_URL in your .env file first")
        return
    
    print("🚀 Starting PostgreSQL checkpoint setup...")
    success = await create_checkpoint_database()
    
    if success:
        print("\n✅ Setup completed successfully!")
        print("Your application will now use PostgreSQL for persistent checkpointing.")
    else:
        print("\n❌ Setup failed. Please check the error messages above.")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
