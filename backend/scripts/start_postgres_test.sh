#!/bin/bash

# Quick PostgreSQL setup for testing LangGraph checkpoints
echo "🐳 Setting up PostgreSQL for LangGraph testing..."

# Stop and remove existing container if it exists
echo "🧹 Cleaning up existing containers..."
docker stop langgraph-postgres 2>/dev/null || true
docker rm langgraph-postgres 2>/dev/null || true

# Pull PostgreSQL image
echo "📦 Pulling PostgreSQL image..."
docker pull postgres:15-alpine

# Start PostgreSQL container
echo "🚀 Starting PostgreSQL container..."
docker run --name langgraph-postgres \
  -e POSTGRES_DB=langgraph_checkpoints \
  -e POSTGRES_USER=testuser \
  -e POSTGRES_PASSWORD=testpass123 \
  -p 5432:5432 \
  -d postgres:15-alpine

# Wait for PostgreSQL to be ready
echo "⏳ Waiting for PostgreSQL to be ready..."
sleep 5

# Test connection
echo "🔍 Testing connection..."
for i in {1..10}; do
  if docker exec langgraph-postgres pg_isready -U testuser -d langgraph_checkpoints >/dev/null 2>&1; then
    echo "✅ PostgreSQL is ready!"
    break
  fi
  echo "   Attempt $i/10 - waiting..."
  sleep 2
done

# Display connection info
echo ""
echo "🎉 PostgreSQL is running!"
echo "📋 Connection Details:"
echo "   Host: localhost"
echo "   Port: 5432"
echo "   Database: langgraph_checkpoints"
echo "   Username: testuser"
echo "   Password: testpass123"
echo ""
echo "🔗 Connection URL:"
echo "   POSTGRES_CHECKPOINT_URL=postgresql://testuser:testpass123@localhost:5432/langgraph_checkpoints"
echo ""
echo "📝 Next steps:"
echo "   1. Add the connection URL to your .env file"
echo "   2. Run: python scripts/setup_postgres_checkpoints.py"
echo "   3. Start your application"
echo ""
echo "🛑 To stop: docker stop langgraph-postgres"
echo "🗑️  To remove: docker rm langgraph-postgres"
