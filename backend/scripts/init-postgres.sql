-- Initialize PostgreSQL database for LangGraph checkpoints
-- This script runs automatically when the Docker container starts

-- Create the database (already created by POSTGRES_DB env var)
-- CREATE DATABASE langgraph_checkpoints;

-- Grant necessary permissions
GRANT ALL PRIVILEGES ON DATABASE langgraph_checkpoints TO langgraph_user;

-- Connect to the database
\c langgraph_checkpoints;

-- <PERSON> schema permissions
GRANT ALL ON SCHEMA public TO langgraph_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO langgraph_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO langgraph_user;

-- Set default privileges for future objects
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO langgraph_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO langgraph_user;

-- The actual checkpoint tables will be created by <PERSON><PERSON><PERSON><PERSON>'s setup() method
