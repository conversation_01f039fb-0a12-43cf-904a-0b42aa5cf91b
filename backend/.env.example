# Database Configuration
MONGODB_URL=mongodb://localhost:27017
DATABASE_NAME=chat_system

# Authentication
SESSION_SECRET_KEY=your-session-secret-key-change-in-production-must-be-32-bytes

# OAuth Configuration
# Google OAuth (get from Google Cloud Console)
GOOGLE_CLIENT_ID=your_google_client_id.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=your_google_client_secret

# GitHub OAuth (get from GitHub Developer Settings)
GITHUB_CLIENT_ID=your_github_client_id
GITHUB_CLIENT_SECRET=your_github_client_secret

# Frontend URL for redirects
FRONTEND_URL=http://localhost:3000

# LangGraph PostgreSQL Checkpointing (Testing/Production)
# For testing with Docker container:
POSTGRES_CHECKPOINT_URL=postgresql://testuser:testpass123@localhost:5432/langgraph_checkpoints

# For production use:
# POSTGRES_CHECKPOINT_URL=postgresql://username:password@localhost:5432/langgraph_checkpoints

# Alternative PostgreSQL connection formats:
# POSTGRES_CHECKPOINT_URL=postgresql://user:pass@localhost/dbname
# POSTGRES_CHECKPOINT_URL=postgresql://user:pass@localhost:5432/dbname?sslmode=require

# For cloud PostgreSQL (e.g., AWS RDS, Google Cloud SQL, Azure):
# POSTGRES_CHECKPOINT_URL=****************************************/dbname?sslmode=require

# LangChain/LangSmith (Optional)
# LANGCHAIN_API_KEY=your-langchain-api-key
# LANGCHAIN_PROJECT=your-project-name

# OpenAI API (if using OpenAI models)
# OPENAI_API_KEY=your-openai-api-key

# Development vs Production
ENVIRONMENT=development

# Logging
LOG_LEVEL=INFO
