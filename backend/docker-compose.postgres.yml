version: '3.8'

services:
  postgres-checkpoints:
    image: postgres:15-alpine
    container_name: langgraph-postgres
    environment:
      POSTGRES_DB: langgraph_checkpoints
      POSTGRES_USER: langgraph_user
      POSTGRES_PASSWORD: langgraph_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_checkpoint_data:/var/lib/postgresql/data
      - ./scripts/init-postgres.sql:/docker-entrypoint-initdb.d/init.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U langgraph_user -d langgraph_checkpoints"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped

volumes:
  postgres_checkpoint_data:
    driver: local

# Usage:
# 1. Start PostgreSQL: docker-compose -f docker-compose.postgres.yml up -d
# 2. Set in .env: POSTGRES_CHECKPOINT_URL=postgresql://langgraph_user:langgraph_password@localhost:5432/langgraph_checkpoints
# 3. Run setup script: python scripts/setup_postgres_checkpoints.py
# 4. Stop PostgreSQL: docker-compose -f docker-compose.postgres.yml down
