"""
FastAPI main application for the chat system with booking and dashboard
"""
import os
from datetime import datetime, timezone
from contextlib import asynccontextmanager

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel

# Local imports
from src.config.database import db_config, initialize_collections, create_sample_time_slots
from src.services.chat_service import get_chat_service
from src.services.global_retriever import get_global_retriever
from src.routes import chat, booking, dashboard



@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan events"""
    # Startup
    print("🚀 Starting Chat System API...")
    
    # Initialize database
    if db_config.connect():
        print("✅ Database connected successfully")
        await initialize_collections()
        await create_sample_time_slots()
        print("✅ Database initialized")
    else:
        print("❌ Failed to connect to database")
        raise Exception("Database connection failed")
    
    # Initialize global retriever first
    try:
        global_retriever = get_global_retriever()
        print("✅ Global retriever initialized")
    except Exception as e:
        print(f"❌ Failed to initialize global retriever: {e}")

    # Initialize chat service
    try:
        chat_service = get_chat_service()
        print("✅ Chat service initialized")
    except Exception as e:
        print(f"❌ Failed to initialize chat service: {e}")
        raise
    
    yield
    
    # Shutdown
    print("🛑 Shutting down Chat System API...")
    db_config.close_connection()
    print("✅ Database connection closed")

# Create FastAPI app
app = FastAPI(
    title="Chat System API",
    description="Advanced chat system with OAuth authentication, booking, analytics, and dashboard",
    version="1.0.0",
    swagger_ui_oauth2_redirect_url="/docs/oauth2-redirect",
    lifespan=lifespan
)

# No session middleware needed for JWT authentication

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://localhost:5173"],  # React dev servers
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Response models
class HealthResponse(BaseModel):
    status: str
    timestamp: str
    version: str
    database_connected: bool

# Health check endpoint
@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint"""
    try:
        # Test database connection
        db = db_config.get_database()
        db.command("ping")
        db_connected = True
    except:
        db_connected = False
    
    return HealthResponse(
        status="healthy" if db_connected else "unhealthy",
        timestamp=datetime.now(timezone.utc).isoformat(),
        version="1.0.0",
        database_connected=db_connected
    )



# Include routers
from src.routes import auth
app.include_router(auth.router, prefix="/api/auth", tags=["Authentication"])
app.include_router(chat.router, prefix="/api/chat", tags=["Chat"])
app.include_router(booking.router, prefix="/api/booking", tags=["Booking"])
app.include_router(dashboard.router, prefix="/api/dashboard", tags=["Dashboard"])

# Root endpoint
@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "Chat System API",
        "version": "1.0.0",
        "docs": "/docs",
        "health": "/health"
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
