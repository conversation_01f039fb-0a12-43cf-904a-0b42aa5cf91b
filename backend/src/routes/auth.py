"""
Modern authentication routes with secure token-based authentication using Argon2
"""
from datetime import datetime, timed<PERSON><PERSON>
from typing import Optional, Union
from fastapi import APIRouter, HTTPException, Depends, status, Request, Response, Header
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials, OAuth2PasswordBearer, OAuth2PasswordRequestForm
from fastapi.responses import JSONResponse
from pydantic import BaseModel, EmailStr
import os
import secrets
import hashlib
from cryptography.fernet import Fernet
import base64
import json

from src.models.user import UserService, UserCreate, UserResponse, UserUpdate, CurrentUser
from src.config.database import get_collection, COLLECTIONS

router = APIRouter()

# Token settings
SECRET_KEY = os.getenv("AUTH_SECRET_KEY", "your-secret-key-change-in-production-must-be-32-bytes")
TOKEN_EXPIRE_HOURS = 24
security = HTTPBearer(auto_error=False)

# OAuth2 scheme for Swagger UI
oauth2_scheme = OAuth2PasswordBearer(
    tokenUrl="/api/auth/login",
    auto_error=False  # Don't automatically return 401, let us handle it
)

# Initialize Fernet for token encryption
def get_fernet():
    """Get Fernet instance for token encryption"""
    key = SECRET_KEY.encode()
    if len(key) < 32:
        key = key.ljust(32, b'0')
    elif len(key) > 32:
        key = key[:32]
    return Fernet(base64.urlsafe_b64encode(key))

# Request/Response models
class LoginRequest(BaseModel):
    email: EmailStr
    password: str

class LoginResponse(BaseModel):
    success: bool
    message: str
    user: Optional[UserResponse] = None
    access_token: Optional[str] = None
    token_type: str = "bearer"

class RegisterRequest(UserCreate):
    pass

class TokenData(BaseModel):
    user_id: str
    email: str
    created_at: datetime
    expires_at: datetime

# Utility functions
async def get_user_service():
    """Get user service instance"""
    users_collection = await get_collection(COLLECTIONS['users'])
    return UserService(users_collection)

def create_access_token(user_id: str, email: str) -> str:
    """Create encrypted access token"""
    expires_at = datetime.utcnow() + timedelta(hours=TOKEN_EXPIRE_HOURS)
    
    token_data = TokenData(
        user_id=user_id,
        email=email,
        created_at=datetime.utcnow(),
        expires_at=expires_at
    )
    
    # Encrypt token data
    fernet = get_fernet()
    token_json = token_data.json()
    encrypted_token = fernet.encrypt(token_json.encode())
    
    return base64.urlsafe_b64encode(encrypted_token).decode()

def verify_token(token: str) -> Optional[TokenData]:
    """Verify and decrypt access token"""
    try:
        # Decode and decrypt token
        encrypted_token = base64.urlsafe_b64decode(token.encode())
        fernet = get_fernet()
        decrypted_data = fernet.decrypt(encrypted_token)
        
        # Parse token data
        token_data = TokenData.parse_raw(decrypted_data)
        
        # Check if token is expired
        if datetime.utcnow() > token_data.expires_at:
            return None
            
        return token_data
        
    except Exception:
        return None

async def get_current_user(token: str = Depends(oauth2_scheme)) -> CurrentUser:
    """Get current authenticated user from token"""
    if not token:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Not authenticated",
            headers={"WWW-Authenticate": "Bearer"},
        )

    token_data = verify_token(token)
    if not token_data:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid or expired token",
            headers={"WWW-Authenticate": "Bearer"},
        )

    user_service = await get_user_service()
    user = await user_service.get_user_by_id(token_data.user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="User not found",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # Return enhanced CurrentUser with database access
    return CurrentUser(user)

# Routes
@router.post("/register", response_model=LoginResponse)
async def register(user_data: RegisterRequest):
    """Register a new user"""
    try:
        user_service = await get_user_service()
        user = await user_service.create_user(user_data)

        # Create access token
        access_token = create_access_token(str(user.id), user.email)

        # Convert to response model
        user_response = UserResponse(
            id=str(user.id),
            name=user.name,
            email=user.email,
            phone=user.phone,
            is_active=user.is_active,
            created_at=user.created_at,
            last_login=user.last_login,
            preferences=user.preferences
        )

        return LoginResponse(
            success=True,
            message="Registration successful",
            user=user_response,
            access_token=access_token
        )

    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Registration failed: {str(e)}"
        )


@router.get("/me", response_model=UserResponse)
async def get_current_user_info(current_user: CurrentUser = Depends(get_current_user)):
    """Get current user information"""
    return current_user.to_response()

@router.get("/check")
async def check_auth(current_user: CurrentUser = Depends(get_current_user)):
    """Check if user is authenticated"""
    return {
        "authenticated": True,
        "user": current_user.to_response(),
        "tenant_info": {
            "tenant_id": current_user.tenant_id,
            "subscription_plan": current_user.subscription_plan,
            "max_conversations": current_user.max_conversations,
            "max_messages_per_day": current_user.max_messages_per_day,
            "conversations_count": await current_user.get_user_conversations_count(),
            "messages_today_count": await current_user.get_user_messages_today_count(),
            "can_create_conversation": await current_user.can_create_conversation(),
            "can_send_message": await current_user.can_send_message()
        }
    }

@router.put("/me", response_model=UserResponse)
async def update_current_user(
    update_data: UserUpdate,
    current_user = Depends(get_current_user)
):
    """Update current user information"""
    try:
        user_service = await get_user_service()
        updated_user = await user_service.update_user(str(current_user.id), update_data)

        if not updated_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to update user"
            )

        return UserResponse(
            id=str(updated_user.id),
            name=updated_user.name,
            email=updated_user.email,
            phone=updated_user.phone,
            is_active=updated_user.is_active,
            created_at=updated_user.created_at,
            last_login=updated_user.last_login,
            preferences=updated_user.preferences
        )

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update user: {str(e)}"
        )

@router.post("/login")
async def login_for_access_token(form_data: OAuth2PasswordRequestForm = Depends()):
    """OAuth2 compatible login endpoint for Swagger UI"""
    user_service = await get_user_service()

    # Authenticate user (form_data.username can be email)
    user = await user_service.authenticate_user(form_data.username, form_data.password)

    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect email or password",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # Create access token
    access_token = create_access_token(str(user.id), user.email)

    return {
        "access_token": access_token,
        "token_type": "bearer",
        "user": {
            "id": str(user.id),
            "name": user.name,
            "email": user.email,
            "is_active": user.is_active
        }
    }

@router.post("/logout")
async def logout():
    """Logout user (client should discard token)"""
    return {"success": True, "message": "Logged out successfully"}
