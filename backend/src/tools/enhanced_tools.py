"""
Enhanced tools with database integration for the chat system
"""
from datetime import datetime, timedelta
from typing import List, Dict, Any
from langchain_core.tools import tool

def get_enhanced_tools(qa_chain, booking_service):
    """Get enhanced tools with database integration"""
    
    @tool
    def search_database(query: str, category: str = "all") -> str:
        """
        Search the database for products or services using Qdrant vector search.

        Args:
            query: The search term to look for
            category: Filter by category (optional, not used in current implementation)

        Returns:
            A formatted string with search results
        """
        try:
            answer = qa_chain.invoke({"input": query})
            result = answer["answer"]
            return result

        except Exception as e:
            return f"Error searching database: {str(e)}"

    @tool
    def get_current_date() -> str:
        """
        Get the current date and time information.
        Use this tool when user asks about dates or for booking processes.

        Returns:
            Current date and time information
        """
        now = datetime.now()
        
        # Format date in multiple ways for clarity
        formatted_date = now.strftime("%A, %B %d, %Y")
        formatted_time = now.strftime("%I:%M %p")
        iso_date = now.strftime("%Y-%m-%d")
        iso_time = now.strftime("%H:%M")
        
        return f"""📅 Current Date & Time Information:

Today is: {formatted_date}
Current time: {formatted_time}
Date (YYYY-MM-DD): {iso_date}
Time (24-hour): {iso_time}

This information is useful for booking appointments and scheduling."""

    @tool
    def get_available_slots(service_type: str = "any", days_ahead: int = 14) -> str:
        """
        Get available appointment slots. This tool automatically gets the current date first.

        Args:
            service_type: Type of service (optional)
            days_ahead: Number of days to look ahead

        Returns:
            List of available appointment slots with current date information
        """
        try:
            # Get current date information
            current_date_info = get_current_date.invoke({})
            
            # Get available slots from database
            available_slots = booking_service.get_available_slots(days_ahead)
            
            if not available_slots:
                return f"{current_date_info}\n\n❌ No available slots found for the next {days_ahead} days."
            
            # Format slots for display
            result = f"{current_date_info}\n\n📅 Available Appointment Slots:\n\n"
            
            for i, slot in enumerate(available_slots[:10]):  # Show first 10 slots
                slot_datetime = slot.datetime
                formatted_slot = slot_datetime.strftime("%B %d, %Y (%A) at %I:%M %p")
                available_spots = slot.max_bookings - slot.current_bookings
                
                result += f"{i+1}. {formatted_slot}\n"
                result += f"   📍 Date: {slot.date}, Time: {slot.time}\n"
                result += f"   🎯 Available spots: {available_spots}/{slot.max_bookings}\n\n"
            
            if len(available_slots) > 10:
                result += f"... and {len(available_slots) - 10} more slots available.\n\n"
            
            result += "💡 To book an appointment, please provide:\n"
            result += "   • Your full name\n"
            result += "   • Email address\n"
            result += "   • Phone number\n"
            result += "   • Preferred date and time from the list above\n"
            result += "   • Type of service needed"
            
            return result
            
        except Exception as e:
            return f"Error retrieving available slots: {str(e)}"

    @tool
    def book_appointment(
        name: str, 
        email: str, 
        phone: str, 
        date: str, 
        time: str, 
        service_type: str = "general_consultation",
        session_id: str = "default",
        user_id: str = None
    ) -> str:
        """
        Book an appointment with customer details.
        
        Args:
            name: Customer's full name
            email: Customer's email address
            phone: Customer's phone number
            date: Preferred date in YYYY-MM-DD format
            time: Preferred time in HH:MM format
            service_type: Type of service needed
            session_id: Chat session ID
            user_id: User ID if logged in
        
        Returns:
            Confirmation message with appointment details
        """
        try:
            # Validate inputs
            if not all([name, email, phone, date, time]):
                return "❌ Error: All fields (name, email, phone, date, time) are required to book an appointment."
            
            # Map service type
            service_mapping = {
                "general_consultation": "GENERAL_CONSULTATION",
                "technical_support": "TECHNICAL_SUPPORT", 
                "course_enrollment": "COURSE_ENROLLMENT",
                "loksewa_preparation": "LOKSEWA_PREPARATION",
                "career_guidance": "CAREER_GUIDANCE"
            }
            
            mapped_service = service_mapping.get(service_type.lower(), "GENERAL_CONSULTATION")
            
            # Create booking
            from models.booking import BookingCreate
            booking_data = BookingCreate(
                name=name,
                email=email,
                phone=phone,
                service_type=mapped_service,
                date=date,
                time=time,
                user_id=user_id,
                session_id=session_id
            )
            
            booking = booking_service.create_booking(booking_data)
            
            return f"""✅ Appointment Successfully Booked!

🎫 Booking Details:
   • Booking ID: {booking.booking_id}
   • Name: {booking.name}
   • Email: {booking.email}
   • Phone: {booking.phone}
   • Date: {booking.date}
   • Time: {booking.time}
   • Service: {booking.service_type.replace('_', ' ').title()}
   • Status: {booking.status.title()}

📧 A confirmation email will be sent to {booking.email}.
📱 Please save your Booking ID: {booking.booking_id} for future reference.

⚠️ Important Notes:
   • Please arrive 10 minutes before your appointment
   • Bring a valid ID for verification
   • Contact us if you need to reschedule or cancel

Thank you for booking with us! 🙏"""
            
        except ValueError as e:
            return f"❌ Booking Error: {str(e)}"
        except Exception as e:
            return f"❌ Unexpected error occurred while booking: {str(e)}"

    @tool
    def check_booking_status(booking_id: str) -> str:
        """
        Check the status of an existing booking.
        
        Args:
            booking_id: The booking ID to check
            
        Returns:
            Booking status and details
        """
        try:
            booking = booking_service.get_booking_by_id(booking_id)
            
            if not booking:
                return f"❌ No booking found with ID: {booking_id}"
            
            return f"""📋 Booking Status:

🎫 Booking ID: {booking.booking_id}
👤 Name: {booking.name}
📧 Email: {booking.email}
📱 Phone: {booking.phone}
📅 Date: {booking.date}
🕐 Time: {booking.time}
🏷️ Service: {booking.service_type.replace('_', ' ').title()}
📊 Status: {booking.status.title()}
📝 Created: {booking.created_at.strftime('%B %d, %Y at %I:%M %p')}

{f"📝 Notes: {booking.notes}" if booking.notes else ""}

💡 Need to make changes? Contact our support team with your booking ID."""
            
        except Exception as e:
            return f"❌ Error checking booking status: {str(e)}"

    @tool
    def cancel_booking(booking_id: str) -> str:
        """
        Cancel an existing booking.
        
        Args:
            booking_id: The booking ID to cancel
            
        Returns:
            Cancellation confirmation
        """
        try:
            success = booking_service.cancel_booking(booking_id)
            
            if success:
                return f"""✅ Booking Cancelled Successfully!

🎫 Booking ID: {booking_id} has been cancelled.
📧 A cancellation confirmation will be sent to your email.
🔄 The time slot is now available for other customers.

💡 You can book a new appointment anytime using the available slots."""
            else:
                return f"❌ Unable to cancel booking {booking_id}. Please check the booking ID or contact support."
                
        except Exception as e:
            return f"❌ Error cancelling booking: {str(e)}"

    return [
        search_database,
        get_current_date,
        get_available_slots,
        book_appointment,
        check_booking_status,
        cancel_booking
    ]
