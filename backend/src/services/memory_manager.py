"""
Production-level Memory Management for Chat System
Based on LangChain's latest memory patterns and best practices
"""
import json
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from langchain_core.messages import BaseMessage, HumanMessage, AIMessage, SystemMessage
from langchain_core.chat_history import BaseChatMessageHistory
from langchain_community.chat_message_histories import ChatMessageHistory
from langchain_core.runnables.history import RunnableWithMessageHistory
from langchain_core.prompts import Chat<PERSON>romptTemplate, MessagesPlaceholder
from langchain_core.runnables import RunnablePassthrough
from langchain_openai import ChatOpenAI
import threading
from collections import defaultdict


class ProductionChatMessageHistory(BaseChatMessageHistory):
    """Production-level chat message history with MongoDB persistence"""
    
    def __init__(self, session_id: str, collection):
        self.session_id = session_id
        self.collection = collection
        self._messages: List[BaseMessage] = []
        self._load_messages()
    
    def _load_messages(self):
        """Load messages from MongoDB"""
        try:
            # Load existing messages for this session
            messages_data = list(self.collection.find(
                {"session_id": self.session_id}
            ).sort("timestamp", 1))
            
            self._messages = []
            for msg_data in messages_data:
                if msg_data["message_type"] == "human":
                    self._messages.append(HumanMessage(content=msg_data["content"]))
                elif msg_data["message_type"] == "ai":
                    self._messages.append(AIMessage(content=msg_data["content"]))
                elif msg_data["message_type"] == "system":
                    self._messages.append(SystemMessage(content=msg_data["content"]))
                    
        except Exception as e:
            print(f"Error loading messages for session {self.session_id}: {e}")
            self._messages = []
    
    def add_message(self, message: BaseMessage) -> None:
        """Add a message to the history and persist to MongoDB"""
        self._messages.append(message)
        
        # Persist to MongoDB
        try:
            message_doc = {
                "session_id": self.session_id,
                "content": message.content,
                "message_type": self._get_message_type(message),
                "timestamp": datetime.now(),
                "metadata": getattr(message, 'additional_kwargs', {})
            }
            self.collection.insert_one(message_doc)
        except Exception as e:
            print(f"Error persisting message: {e}")
    
    def _get_message_type(self, message: BaseMessage) -> str:
        """Get message type string"""
        if isinstance(message, HumanMessage):
            return "human"
        elif isinstance(message, AIMessage):
            return "ai"
        elif isinstance(message, SystemMessage):
            return "system"
        else:
            return "unknown"
    
    @property
    def messages(self) -> List[BaseMessage]:
        """Get all messages"""
        return self._messages
    
    def clear(self) -> None:
        """Clear all messages"""
        self._messages = []
        try:
            self.collection.delete_many({"session_id": self.session_id})
        except Exception as e:
            print(f"Error clearing messages: {e}")


class MemoryManager:
    """Production-level memory manager with session management"""
    
    def __init__(self, messages_collection):
        self.messages_collection = messages_collection
        self.session_histories: Dict[str, ProductionChatMessageHistory] = {}
        self.session_locks = defaultdict(threading.Lock)
        self.cleanup_interval = 3600  # 1 hour
        self.max_session_age = timedelta(hours=24)  # 24 hours
        self._start_cleanup_task()
    
    def get_session_history(self, session_id: str) -> ProductionChatMessageHistory:
        """Get or create session history"""
        with self.session_locks[session_id]:
            if session_id not in self.session_histories:
                self.session_histories[session_id] = ProductionChatMessageHistory(
                    session_id, self.messages_collection
                )
            return self.session_histories[session_id]
    
    def create_conversational_chain(self, llm, system_prompt: str):
        """Create a conversational chain with memory"""
        
        # Create prompt template
        prompt = ChatPromptTemplate.from_messages([
            ("system", system_prompt),
            MessagesPlaceholder(variable_name="history"),
            ("human", "{input}"),
        ])
        
        # Create the chain
        chain = prompt | llm
        
        # Wrap with message history
        chain_with_history = RunnableWithMessageHistory(
            chain,
            self.get_session_history,
            input_messages_key="input",
            history_messages_key="history",
        )
        
        return chain_with_history
    
    def get_session_summary(self, session_id: str, max_messages: int = 10) -> Dict[str, Any]:
        """Get session summary with recent messages"""
        history = self.get_session_history(session_id)
        recent_messages = history.messages[-max_messages:] if history.messages else []
        
        return {
            "session_id": session_id,
            "total_messages": len(history.messages),
            "recent_messages": [
                {
                    "type": msg.__class__.__name__,
                    "content": msg.content[:100] + "..." if len(msg.content) > 100 else msg.content
                }
                for msg in recent_messages
            ]
        }
    
    def cleanup_old_sessions(self):
        """Clean up old sessions from memory (not from database)"""
        try:
            cutoff_time = datetime.now() - self.max_session_age
            
            # Get sessions to remove from memory
            sessions_to_remove = []
            for session_id in list(self.session_histories.keys()):
                # Check last activity from database
                last_message = self.messages_collection.find_one(
                    {"session_id": session_id},
                    sort=[("timestamp", -1)]
                )
                
                if last_message and last_message["timestamp"] < cutoff_time:
                    sessions_to_remove.append(session_id)
            
            # Remove from memory
            for session_id in sessions_to_remove:
                if session_id in self.session_histories:
                    del self.session_histories[session_id]
                if session_id in self.session_locks:
                    del self.session_locks[session_id]
            
            if sessions_to_remove:
                print(f"🧹 Cleaned up {len(sessions_to_remove)} old sessions from memory")
                
        except Exception as e:
            print(f"Error during session cleanup: {e}")
    
    def _start_cleanup_task(self):
        """Start background cleanup task"""
        def cleanup_loop():
            while True:
                try:
                    self.cleanup_old_sessions()
                except Exception as e:
                    print(f"Error in cleanup loop: {e}")
        
        # Start cleanup thread
        cleanup_thread = threading.Thread(target=cleanup_loop, daemon=True)
        cleanup_thread.start()
    
    def get_active_sessions_count(self) -> int:
        """Get count of active sessions in memory"""
        return len(self.session_histories)
    
    def force_cleanup_session(self, session_id: str):
        """Force cleanup of a specific session"""
        with self.session_locks[session_id]:
            if session_id in self.session_histories:
                del self.session_histories[session_id]
            if session_id in self.session_locks:
                del self.session_locks[session_id]
