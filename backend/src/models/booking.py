"""
Booking model for the appointment system
"""
from datetime import datetime, date, time
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field, EmailStr
from bson import ObjectId
from enum import Enum

from pydantic import GetJsonSchemaHandler
from pydantic.json_schema import JsonSchemaValue
from pydantic_core import core_schema
from typing import Any

class PyObjectId(ObjectId):
    @classmethod
    def __get_pydantic_core_schema__(
        cls, source_type: Any, handler: Any
    ) -> core_schema.CoreSchema:
        return core_schema.json_or_python_schema(
            json_schema=core_schema.str_schema(),
            python_schema=core_schema.union_schema([
                core_schema.is_instance_schema(ObjectId),
                core_schema.chain_schema([
                    core_schema.str_schema(),
                    core_schema.no_info_plain_validator_function(cls.validate),
                ])
            ]),
            serialization=core_schema.plain_serializer_function_ser_schema(
                lambda x: str(x)
            ),
        )

    @classmethod
    def validate(cls, v):
        if isinstance(v, ObjectId):
            return v
        if isinstance(v, str) and ObjectId.is_valid(v):
            return ObjectId(v)
        raise ValueError("Invalid ObjectId")

    @classmethod
    def __get_pydantic_json_schema__(
        cls, schema: core_schema.CoreSchema, handler: GetJsonSchemaHandler
    ) -> JsonSchemaValue:
        return {"type": "string"}

class BookingStatus(str, Enum):
    PENDING = "pending"
    CONFIRMED = "confirmed"
    CANCELLED = "cancelled"
    COMPLETED = "completed"
    NO_SHOW = "no_show"

class ServiceType(str, Enum):
    GENERAL_CONSULTATION = "general_consultation"
    TECHNICAL_SUPPORT = "technical_support"
    COURSE_ENROLLMENT = "course_enrollment"
    LOKSEWA_PREPARATION = "loksewa_preparation"
    CAREER_GUIDANCE = "career_guidance"

class TimeSlotBase(BaseModel):
    """Base time slot model"""
    date: str = Field(..., description="Date in YYYY-MM-DD format")
    time: str = Field(..., description="Time in HH:MM format")
    
class TimeSlotInDB(TimeSlotBase):
    """Time slot model as stored in database"""
    id: PyObjectId = Field(default_factory=PyObjectId, alias="_id")
    datetime: datetime
    available: bool = True
    max_bookings: int = 5
    current_bookings: int = 0
    created_at: datetime = Field(default_factory=datetime.utcnow)
    
    class Config:
        populate_by_name = True
        arbitrary_types_allowed = True
        json_encoders = {ObjectId: str}

class BookingBase(BaseModel):
    """Base booking model"""
    name: str = Field(..., min_length=2, max_length=100)
    email: EmailStr
    phone: str = Field(..., min_length=10, max_length=15)
    service_type: ServiceType
    date: str = Field(..., description="Booking date in YYYY-MM-DD format")
    time: str = Field(..., description="Booking time in HH:MM format")
    notes: Optional[str] = Field(None, max_length=500)

class BookingCreate(BookingBase):
    """Booking creation model"""
    user_id: Optional[str] = None
    session_id: str

class BookingUpdate(BaseModel):
    """Booking update model"""
    status: Optional[BookingStatus] = None
    notes: Optional[str] = Field(None, max_length=500)

class BookingInDB(BookingBase):
    """Booking model as stored in database"""
    id: PyObjectId = Field(default_factory=PyObjectId, alias="_id")
    booking_id: str  # Human-readable booking ID
    user_id: Optional[str] = None
    session_id: str
    status: BookingStatus = BookingStatus.CONFIRMED
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    
    class Config:
        populate_by_name = True
        arbitrary_types_allowed = True
        json_encoders = {ObjectId: str}

class BookingResponse(BookingBase):
    """Booking response model"""
    id: str
    booking_id: str
    status: BookingStatus
    created_at: datetime

class BookingService:
    """Booking service for database operations"""
    
    def __init__(self, booking_collection, time_slot_collection):
        self.booking_collection = booking_collection
        self.time_slot_collection = time_slot_collection
    
    def generate_booking_id(self) -> str:
        """Generate human-readable booking ID"""
        count = self.booking_collection.count_documents({})
        return f"BK-{count + 1:06d}"
    
    def get_available_slots(self, days_ahead: int = 14) -> List[TimeSlotInDB]:
        """Get available time slots"""
        from datetime import datetime, timedelta
        
        start_date = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        end_date = start_date + timedelta(days=days_ahead)
        
        slots = self.time_slot_collection.find({
            "datetime": {"$gte": start_date, "$lte": end_date},
            "available": True,
            "$expr": {"$lt": ["$current_bookings", "$max_bookings"]}
        }).sort("datetime", 1)
        
        return [TimeSlotInDB(**slot) for slot in slots]
    
    def check_slot_availability(self, date_str: str, time_str: str) -> Optional[TimeSlotInDB]:
        """Check if a specific slot is available"""
        slot = self.time_slot_collection.find_one({
            "date": date_str,
            "time": time_str,
            "available": True,
            "$expr": {"$lt": ["$current_bookings", "$max_bookings"]}
        })
        
        if slot:
            return TimeSlotInDB(**slot)
        return None
    
    def create_booking(self, booking_data: BookingCreate) -> BookingInDB:
        """Create new booking"""
        # Check slot availability
        slot = self.check_slot_availability(booking_data.date, booking_data.time)
        if not slot:
            raise ValueError(f"No available slot for {booking_data.date} at {booking_data.time}")
        
        # Check for duplicate booking (same email, date, time)
        existing_booking = self.booking_collection.find_one({
            "email": booking_data.email,
            "date": booking_data.date,
            "time": booking_data.time,
            "status": {"$in": [BookingStatus.CONFIRMED, BookingStatus.PENDING]}
        })
        
        if existing_booking:
            raise ValueError("You already have a booking for this time slot")
        
        # Generate booking ID
        booking_id = self.generate_booking_id()
        
        # Create booking document
        booking_doc = {
            "booking_id": booking_id,
            "name": booking_data.name,
            "email": booking_data.email,
            "phone": booking_data.phone,
            "service_type": booking_data.service_type,
            "date": booking_data.date,
            "time": booking_data.time,
            "notes": booking_data.notes,
            "user_id": booking_data.user_id,
            "session_id": booking_data.session_id,
            "status": BookingStatus.CONFIRMED,
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow()
        }
        
        # Insert booking
        result = self.booking_collection.insert_one(booking_doc)
        booking_doc["_id"] = result.inserted_id
        
        # Update slot booking count
        self.time_slot_collection.update_one(
            {"_id": slot.id},
            {"$inc": {"current_bookings": 1}}
        )
        
        return BookingInDB(**booking_doc)
    
    def get_booking_by_id(self, booking_id: str) -> Optional[BookingInDB]:
        """Get booking by booking ID"""
        booking_doc = self.booking_collection.find_one({"booking_id": booking_id})
        if booking_doc:
            return BookingInDB(**booking_doc)
        return None
    
    def get_user_bookings(self, user_id: str) -> List[BookingInDB]:
        """Get bookings by user ID"""
        bookings = self.booking_collection.find(
            {"user_id": user_id}
        ).sort("created_at", -1)
        
        return [BookingInDB(**booking) for booking in bookings]
    
    def get_bookings_by_email(self, email: str) -> List[BookingInDB]:
        """Get bookings by email"""
        bookings = self.booking_collection.find(
            {"email": email}
        ).sort("created_at", -1)
        
        return [BookingInDB(**booking) for booking in bookings]
    
    def update_booking_status(self, booking_id: str, status: BookingStatus) -> Optional[BookingInDB]:
        """Update booking status"""
        result = self.booking_collection.update_one(
            {"booking_id": booking_id},
            {
                "$set": {
                    "status": status,
                    "updated_at": datetime.utcnow()
                }
            }
        )
        
        if result.modified_count:
            return self.get_booking_by_id(booking_id)
        return None
    
    def cancel_booking(self, booking_id: str) -> bool:
        """Cancel a booking and free up the slot"""
        booking = self.get_booking_by_id(booking_id)
        if not booking:
            return False
        
        # Update booking status
        result = self.booking_collection.update_one(
            {"booking_id": booking_id},
            {
                "$set": {
                    "status": BookingStatus.CANCELLED,
                    "updated_at": datetime.utcnow()
                }
            }
        )
        
        if result.modified_count:
            # Decrease slot booking count
            self.time_slot_collection.update_one(
                {"date": booking.date, "time": booking.time},
                {"$inc": {"current_bookings": -1}}
            )
            return True
        
        return False
    
    def get_daily_bookings(self, date_str: str) -> List[BookingInDB]:
        """Get all bookings for a specific date"""
        bookings = self.booking_collection.find(
            {"date": date_str}
        ).sort("time", 1)
        
        return [BookingInDB(**booking) for booking in bookings]
