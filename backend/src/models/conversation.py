"""
Conversation model for tracking chat conversations with enhanced metadata and analytics
"""
from datetime import datetime
from typing import Optional, Dict, Any, List
from pydantic import BaseModel, Field
from bson import ObjectId
from enum import Enum

from pydantic import GetJsonSchemaHandler
from pydantic.json_schema import JsonSchemaValue
from pydantic_core import core_schema
from typing import Any

class PyObjectId(ObjectId):
    """Custom ObjectId type for Pydantic"""
    
    @classmethod
    def __get_validators__(cls):
        yield cls.validate
    
    @classmethod
    def validate(cls, v):
        if not ObjectId.is_valid(v):
            raise ValueError("Invalid ObjectId")
        return ObjectId(v)
    
    @classmethod
    def __modify_schema__(cls, field_schema):
        field_schema.update(type="string")
    
    @classmethod
    def __get_pydantic_core_schema__(
        cls, source_type: Any, handler
    ) -> core_schema.CoreSchema:
        return core_schema.json_or_python_schema(
            json_schema=core_schema.str_schema(),
            python_schema=core_schema.union_schema([
                core_schema.is_instance_schema(ObjectId),
                core_schema.chain_schema([
                    core_schema.str_schema(),
                    core_schema.no_info_plain_validator_function(cls.validate),
                ])
            ]),
            serialization=core_schema.plain_serializer_function_ser_schema(
                lambda x: str(x)
            ),
        )

    @classmethod
    def __get_pydantic_json_schema__(
        cls, schema: core_schema.CoreSchema, handler: GetJsonSchemaHandler
    ) -> JsonSchemaValue:
        return {"type": "string"}

class ConversationStatus(str, Enum):
    ACTIVE = "active"
    ARCHIVED = "archived"
    DELETED = "deleted"

class ConversationType(str, Enum):
    CHAT = "chat"
    SUPPORT = "support"
    BOOKING = "booking"
    CONSULTATION = "consultation"

class ConversationParticipant(BaseModel):
    """Participant in a conversation"""
    user_id: Optional[str] = None
    role: str = Field(default="user")  # user, assistant, admin, etc.
    joined_at: datetime = Field(default_factory=datetime.utcnow)
    last_seen: Optional[datetime] = None
    is_active: bool = True

class ConversationMetrics(BaseModel):
    """Metrics and analytics for a conversation"""
    total_messages: int = 0
    user_messages: int = 0
    assistant_messages: int = 0
    system_messages: int = 0
    average_response_time_ms: Optional[float] = None
    sentiment_score: Optional[float] = None
    satisfaction_rating: Optional[int] = None
    resolution_status: Optional[str] = None
    tags: List[str] = Field(default_factory=list)

class ConversationBase(BaseModel):
    """Base conversation model"""
    title: Optional[str] = None
    conversation_type: ConversationType = ConversationType.CHAT
    status: ConversationStatus = ConversationStatus.ACTIVE

class ConversationCreate(ConversationBase):
    """Conversation creation model"""
    session_id: str
    user_id: Optional[str] = None
    initial_message: Optional[str] = None

class ConversationInDB(ConversationBase):
    """Conversation model as stored in database"""
    id: PyObjectId = Field(default_factory=PyObjectId, alias="_id")
    session_id: str = Field(..., unique=True)
    participants: List[ConversationParticipant] = Field(default_factory=list)
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    last_message_at: Optional[datetime] = None
    metrics: ConversationMetrics = Field(default_factory=ConversationMetrics)
    metadata: Dict[str, Any] = Field(default_factory=dict)
    
    class Config:
        populate_by_name = True
        arbitrary_types_allowed = True
        json_encoders = {ObjectId: str}

class ConversationResponse(ConversationBase):
    """Conversation response model"""
    id: str
    session_id: str
    participants: List[ConversationParticipant]
    created_at: datetime
    updated_at: datetime
    last_message_at: Optional[datetime]
    metrics: ConversationMetrics
    preview_message: Optional[str] = None  # Last message preview

class ConversationUpdate(BaseModel):
    """Conversation update model"""
    title: Optional[str] = None
    status: Optional[ConversationStatus] = None
    conversation_type: Optional[ConversationType] = None
    metadata: Optional[Dict[str, Any]] = None

class ConversationService:
    """Service for managing conversations"""
    
    def __init__(self, conversation_collection, message_collection):
        self.conversation_collection = conversation_collection
        self.message_collection = message_collection
    
    async def create_conversation(self, conversation_data: ConversationCreate) -> ConversationInDB:
        """Create a new conversation"""
        # Check if conversation with session_id already exists
        existing = await self.conversation_collection.find_one({"session_id": conversation_data.session_id})
        if existing:
            return ConversationInDB(**existing)
        
        # Create participants list
        participants = []
        if conversation_data.user_id:
            participants.append(ConversationParticipant(
                user_id=conversation_data.user_id,
                role="user"
            ))
        
        # Add assistant participant
        participants.append(ConversationParticipant(
            user_id=None,
            role="assistant"
        ))
        
        # Generate title if not provided
        title = conversation_data.title
        if not title and conversation_data.initial_message:
            # Use first 50 characters of initial message as title
            title = conversation_data.initial_message[:50] + ("..." if len(conversation_data.initial_message) > 50 else "")
        elif not title:
            title = f"Conversation {datetime.utcnow().strftime('%Y-%m-%d %H:%M')}"
        
        # Create conversation document
        conversation_doc = {
            "title": title,
            "session_id": conversation_data.session_id,
            "conversation_type": conversation_data.conversation_type,
            "status": conversation_data.status,
            "participants": [p.dict() for p in participants],
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow(),
            "last_message_at": None,
            "metrics": ConversationMetrics().dict(),
            "metadata": {}
        }
        
        result = await self.conversation_collection.insert_one(conversation_doc)
        conversation_doc["_id"] = result.inserted_id
        
        return ConversationInDB(**conversation_doc)
    
    async def get_conversation_by_session(self, session_id: str) -> Optional[ConversationInDB]:
        """Get conversation by session ID"""
        conversation = await self.conversation_collection.find_one({"session_id": session_id})
        if conversation:
            return ConversationInDB(**conversation)
        return None
    
    async def update_conversation_metrics(self, session_id: str):
        """Update conversation metrics based on messages"""
        # Get all messages for this session
        messages = list(self.message_collection.find({"session_id": session_id}))
        
        if not messages:
            return
        
        # Calculate metrics
        total_messages = len(messages)
        user_messages = len([m for m in messages if m.get("message_type") == "user"])
        assistant_messages = len([m for m in messages if m.get("message_type") == "assistant"])
        system_messages = len([m for m in messages if m.get("message_type") == "system"])
        
        # Calculate average response time
        response_times = []
        for msg in messages:
            if msg.get("analytics") and msg["analytics"].get("response_time_ms"):
                response_times.append(msg["analytics"]["response_time_ms"])
        
        avg_response_time = sum(response_times) / len(response_times) if response_times else None
        
        # Calculate average sentiment
        sentiment_scores = []
        for msg in messages:
            if msg.get("analytics") and msg["analytics"].get("sentiment_score"):
                sentiment_scores.append(msg["analytics"]["sentiment_score"])
        
        avg_sentiment = sum(sentiment_scores) / len(sentiment_scores) if sentiment_scores else None
        
        # Get last message timestamp
        last_message_at = max([m.get("timestamp") for m in messages if m.get("timestamp")])
        
        # Update conversation
        update_data = {
            "updated_at": datetime.utcnow(),
            "last_message_at": last_message_at,
            "metrics.total_messages": total_messages,
            "metrics.user_messages": user_messages,
            "metrics.assistant_messages": assistant_messages,
            "metrics.system_messages": system_messages,
        }
        
        if avg_response_time:
            update_data["metrics.average_response_time_ms"] = avg_response_time
        
        if avg_sentiment:
            update_data["metrics.sentiment_score"] = avg_sentiment
        
        await self.conversation_collection.update_one(
            {"session_id": session_id},
            {"$set": update_data}
        )

    async def get_user_conversations(self, user_id: str, limit: int = 50, skip: int = 0) -> List[ConversationInDB]:
        """Get conversations for a user"""
        conversations = self.conversation_collection.find(
            {"participants.user_id": user_id}
        ).sort("last_message_at", -1).skip(skip).limit(limit)

        result = []
        async for conv in conversations:
            result.append(ConversationInDB(**conv))

        return result

    async def update_conversation(self, session_id: str, update_data: ConversationUpdate) -> Optional[ConversationInDB]:
        """Update conversation"""
        update_dict = {k: v for k, v in update_data.dict().items() if v is not None}
        update_dict["updated_at"] = datetime.utcnow()

        result = await self.conversation_collection.update_one(
            {"session_id": session_id},
            {"$set": update_dict}
        )

        if result.modified_count > 0:
            return await self.get_conversation_by_session(session_id)
        return None

    async def delete_conversation(self, session_id: str) -> bool:
        """Soft delete conversation"""
        result = await self.conversation_collection.update_one(
            {"session_id": session_id},
            {"$set": {"status": ConversationStatus.DELETED, "updated_at": datetime.utcnow()}}
        )
        return result.modified_count > 0

    async def get_conversation_with_preview(self, session_id: str) -> Optional[ConversationResponse]:
        """Get conversation with last message preview"""
        conversation = await self.get_conversation_by_session(session_id)
        if not conversation:
            return None

        # Get last message for preview
        last_message = self.message_collection.find_one(
            {"session_id": session_id},
            sort=[("timestamp", -1)]
        )

        preview_message = None
        if last_message:
            preview_message = last_message.get("content", "")[:100]
            if len(last_message.get("content", "")) > 100:
                preview_message += "..."

        return ConversationResponse(
            id=str(conversation.id),
            title=conversation.title,
            session_id=conversation.session_id,
            conversation_type=conversation.conversation_type,
            status=conversation.status,
            participants=conversation.participants,
            created_at=conversation.created_at,
            updated_at=conversation.updated_at,
            last_message_at=conversation.last_message_at,
            metrics=conversation.metrics,
            preview_message=preview_message
        )
